# 校园网络配置指令
# 项目3 - 完整校园网搭建配置

## 网络规划
# VLAN划分:
# VLAN 10: 管理VLAN (************/24)
# VLAN 20: 教学VLAN (************/24) 
# VLAN 30: 办公VLAN (************/24)
# VLAN 40: 学生VLAN (************/24)
# VLAN 50: 服务器VLAN (************/24)
# VLAN 60: 无线VLAN (************/24)
# VLAN 100: DMZ区域 (*************/24)

## 1. 防火墙FW1配置
[FW1]
# 基础配置
sysname FW1
clock timezone BJ add 08:00:00

# 接口配置
interface GigabitEthernet0/0/0
 ip address *********** *************
 service-manage ping permit
 service-manage ssh permit
 service-manage https permit

interface GigabitEthernet1/0/0
 ip address ******** ***************
 
interface GigabitEthernet1/0/1
 ip address ******** ***************

interface GigabitEthernet1/0/2
 ip address ************ *************

# 安全区域配置
firewall zone trust
 add interface GigabitEthernet1/0/0
 add interface GigabitEthernet1/0/1
 add interface GigabitEthernet1/0/2

firewall zone untrust
 add interface GigabitEthernet0/0/0

firewall zone dmz
 add interface GigabitEthernet1/0/3

# 安全策略
security-policy
 rule name trust_to_untrust
  source-zone trust
  destination-zone untrust
  action permit

 rule name dmz_to_untrust
  source-zone dmz
  destination-zone untrust
  action permit

 rule name untrust_to_dmz
  source-zone untrust
  destination-zone dmz
  destination-address ************* mask *************
  action permit

# NAT配置
nat-policy
 rule name trust_nat
  source-zone trust
  destination-zone untrust
  action source-nat easy-ip

# 路由配置
ip route-static 0.0.0.0 0.0.0.0 *************
ip route-static ************ ************* ********
ip route-static ************ ************* ********
ip route-static ************ ************* ********
ip route-static ************ ************* ********

## 2. 路由器AR1配置
[AR1]
sysname AR1
clock timezone BJ add 08:00:00

# 接口配置
interface GigabitEthernet0/0/0
 ip address ******** ***************

interface GigabitEthernet0/0/1
 ip address ******** ***************

interface Ethernet0/0/0
 ip address ******** ***************

# OSPF配置
ospf 1 router-id *******
 area 0.0.0.0
  network ******** *******
  network ******** *******
  network ******** *******

# 静态路由
ip route-static 0.0.0.0 0.0.0.0 ********

## 3. 路由器AR2配置
[AR2]
sysname AR2
clock timezone BJ add 08:00:00

# 接口配置
interface GigabitEthernet0/0/0
 ip address ******** ***************

interface GigabitEthernet0/0/1
 ip address ******** ***************

interface Ethernet0/0/0
 ip address ******** ***************

# OSPF配置
ospf 1 router-id *******
 area 0.0.0.0
  network ******** *******
  network ******** *******
  network ******** *******

# 静态路由
ip route-static 0.0.0.0 0.0.0.0 ********

## 4. 核心交换机LSW1配置
[LSW1]
sysname LSW1
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 10 20 30 40 50 60 100

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AR1"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW3"

interface GigabitEthernet0/0/3
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW2 - Eth-Trunk1"

interface GigabitEthernet0/0/4
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW2 - Eth-Trunk1"

interface GigabitEthernet0/0/5
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AR2"

interface GigabitEthernet0/0/6
 port link-type access
 port default vlan 60
 description "To AP1"

# 链路聚合配置
interface Eth-Trunk1
 port link-type trunk
 port trunk allow-pass vlan all
 mode lacp-static

interface GigabitEthernet0/0/3
 eth-trunk 1

interface GigabitEthernet0/0/4
 eth-trunk 1

# VLAN接口配置
interface Vlanif10
 ip address ************ *************
 dhcp select relay
 dhcp relay destination-ip *************

interface Vlanif20
 ip address ************ *************
 dhcp select relay
 dhcp relay destination-ip *************

interface Vlanif30
 ip address ************ *************
 dhcp select relay
 dhcp relay destination-ip *************

# STP配置
stp mode rstp
stp root primary

# OSPF配置
ospf 1 router-id *******
 area 0.0.0.0
  network ******** *******
  network ************ *********
  network ************ *********
  network ************ *********

## 5. 核心交换机LSW2配置
[LSW2]
sysname LSW2
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 10 20 30 40 50 60 100

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AR2"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW4"

interface GigabitEthernet0/0/3
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW1 - Eth-Trunk1"

interface GigabitEthernet0/0/4
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW1 - Eth-Trunk1"

interface GigabitEthernet0/0/5
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AR1"

interface GigabitEthernet0/0/6
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AC1"

# 链路聚合配置
interface Eth-Trunk1
 port link-type trunk
 port trunk allow-pass vlan all
 mode lacp-static

interface GigabitEthernet0/0/3
 eth-trunk 1

interface GigabitEthernet0/0/4
 eth-trunk 1

# VLAN接口配置
interface Vlanif40
 ip address ************ *************
 dhcp select relay
 dhcp relay destination-ip *************

interface Vlanif60
 ip address ************ *************
 dhcp select relay
 dhcp relay destination-ip *************

interface Vlanif100
 ip address ************* *************

# STP配置
stp mode rstp
stp root secondary

# OSPF配置
ospf 1 router-id *******
 area 0.0.0.0
  network ******** *******
  network ************ *********
  network ************ *********
  network ************* *********

## 6. 接入交换机LSW3配置
[LSW3]
sysname LSW3
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 10 20 30

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW1"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW5"

# 端口安全配置
interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 2
 port-security violation restrict
 description "Teaching Area"

interface GigabitEthernet0/0/4
 port link-type access
 port default vlan 30
 port-security enable
 port-security max-mac-num 1
 port-security violation restrict
 description "Office Area"

# STP配置
stp mode rstp

## 7. 接入交换机LSW4配置
[LSW4]
sysname LSW4
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 10 40 60

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW2"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW6"

# 端口安全配置
interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 40
 port-security enable
 port-security max-mac-num 2
 port-security violation restrict
 description "Student Area"

# STP配置
stp mode rstp

## 8. 接入交换机LSW5配置
[LSW5]
sysname LSW5
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 20

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW3"

interface Ethernet0/0/1
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security violation restrict
 description "To PC1"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW7"

# STP配置
stp mode rstp

## 9. 接入交换机LSW6配置
[LSW6]
sysname LSW6
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 40

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW4"

interface Ethernet0/0/1
 port link-type access
 port default vlan 40
 port-security enable
 port-security max-mac-num 1
 port-security violation restrict
 description "To PC2"

interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW8"

# STP配置
stp mode rstp

## 10. 接入交换机LSW7配置
[LSW7]
sysname LSW7
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 20

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW5"

interface Ethernet0/0/1
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security violation restrict
 description "To PC3"

# STP配置
stp mode rstp

## 11. 接入交换机LSW8配置
[LSW8]
sysname LSW8
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 40

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW6"

interface Ethernet0/0/1
 port link-type access
 port default vlan 40
 port-security enable
 port-security max-mac-num 1
 port-security violation restrict
 description "To PC4"

# STP配置
stp mode rstp

## 12. 服务器区域交换机LSW9配置
[LSW9]
sysname LSW9
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 50

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To FW1"

interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 50
 description "To Server1"

interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 50
 description "To Server2"

# STP配置
stp mode rstp

## 13. 外网区域交换机LSW10配置
[LSW10]
sysname LSW10
clock timezone BJ add 08:00:00

# VLAN配置
vlan batch 100

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To AR3"

interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 100
 description "To Client1"

interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 100
 description "To PC5"

# STP配置
stp mode rstp

## 14. 外网路由器AR3配置
[AR3]
sysname AR3
clock timezone BJ add 08:00:00

# 接口配置
interface GigabitEthernet0/0/0
 ip address ******** ***************

interface GigabitEthernet0/0/1
 ip address ************* *************

# 静态路由
ip route-static 0.0.0.0 0.0.0.0 ********
ip route-static *********** *********** ********

## 15. 无线控制器AC1配置
[AC1]
sysname AC1
clock timezone BJ add 08:00:00

# 基础配置
capwap source interface vlanif 60

# VLAN配置
vlan batch 60

interface vlanif 60
 ip address ************* *************

# 接口配置
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan all
 description "To LSW2"

# 无线配置
wlan
 security-profile name student_security
  security wpa2 psk pass-phrase Huawei@123 aes

 security-profile name guest_security
  security wpa2 psk pass-phrase Guest@123 aes

 ssid-profile name student_ssid
  ssid Campus-Student

 ssid-profile name guest_ssid
  ssid Campus-Guest

 vap-profile name student_vap
  forward-mode tunnel
  service-vlan vlan-id 40
  ssid-profile student_ssid
  security-profile student_security

 vap-profile name guest_vap
  forward-mode tunnel
  service-vlan vlan-id 60
  ssid-profile guest_ssid
  security-profile guest_security

# AP配置
ap auth-mode mac-auth
ap-mac 00-E0-FC-B2-4A-B0
 ap-name AP1
 ap-group default

ap-group name default
 vap-profile student_vap wlan 1 radio 0
 vap-profile guest_vap wlan 2 radio 0

## 16. DHCP服务器配置(Server1)
# 在Server1上配置DHCP服务
# IP: *************/24
# Gateway: ************

# DHCP地址池配置
dhcp enable

ip pool vlan10
 gateway-list ************
 network ************ mask *************
 dns-list ************1 *******
 lease day 7

ip pool vlan20
 gateway-list ************
 network ************ mask *************
 dns-list ************1 *******
 lease day 7

ip pool vlan30
 gateway-list ************
 network ************ mask *************
 dns-list ************1 *******
 lease day 7

ip pool vlan40
 gateway-list ************
 network ************ mask *************
 dns-list ************1 *******
 lease day 7

ip pool vlan60
 gateway-list ************
 network ************ mask *************
 dns-list ************1 *******
 lease day 1

dhcp server ip-pool vlan10
dhcp server ip-pool vlan20
dhcp server ip-pool vlan30
dhcp server ip-pool vlan40
dhcp server ip-pool vlan60

## 17. DNS/FTP服务器配置(Server2)
# 在Server2上配置DNS和FTP服务
# IP: ************1/24
# Gateway: ************

# DNS服务配置
dns server enable
dns domain campus.local

# DNS记录配置
dns host www.campus.local ************1
dns host ftp.campus.local ************1
dns host mail.campus.local ************2

# FTP服务配置
ftp server enable
ftp server acl 3000

## 18. ACL访问控制列表配置

# 在LSW1上配置ACL
[LSW1]
# 基础ACL - 限制学生VLAN访问
acl number 2000
 description "Student Access Control"
 rule 5 deny source ************ ********* destination ************ *********
 rule 10 deny source ************ ********* destination ************ *********
 rule 15 permit source any

# 高级ACL - 限制特定服务
acl number 3000
 description "Service Access Control"
 rule 5 permit tcp source any destination ************1 0 destination-port eq ftp
 rule 10 permit tcp source ************ ********* destination ************1 0 destination-port eq ftp
 rule 15 permit tcp source ************ ********* destination ************1 0 destination-port eq ftp
 rule 20 deny tcp source any destination ************1 0 destination-port eq ftp

# 应用ACL到接口
interface Vlanif40
 traffic-filter inbound acl 2000

# 在LSW2上配置ACL
[LSW2]
# 无线访问控制
acl number 2001
 description "Wireless Access Control"
 rule 5 permit source ************ ********* destination ************ *********
 rule 10 permit source ************ ********* destination any
 rule 15 deny source any destination ************ *********

interface Vlanif60
 traffic-filter inbound acl 2001

## 19. 堆栈配置(可选 - 如果需要设备堆栈)
# 在LSW1和LSW2之间配置CSS堆栈(如果支持)
[LSW1]
# CSS堆栈配置
css enable
css domain-id 1
css member 1 priority 200

[LSW2]
css enable
css domain-id 1
css member 2 priority 100

## 20. 网络管理和监控配置

# SNMP配置
[LSW1]
snmp-agent
snmp-agent local-engineid 800007DB03000000000000
snmp-agent community read public
snmp-agent community write private
snmp-agent sys-info version all

# NTP配置
ntp-service unicast-server *************

# 日志配置
info-center enable
info-center loghost *************

[LSW2]
snmp-agent
snmp-agent local-engineid 800007DB03000000000001
snmp-agent community read public
snmp-agent community write private
snmp-agent sys-info version all

ntp-service unicast-server *************
info-center enable
info-center loghost *************

## 21. 安全加固配置

# 在所有交换机上配置基础安全
# SSH配置
[LSW1]
ssh server enable
ssh user admin
ssh user admin authentication-type password
ssh user admin service-type stelnet

user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh

# AAA配置
aaa
 local-user admin password cipher Huawei@123
 local-user admin privilege level 15
 local-user admin service-type ssh

# 端口安全全局配置
port-security enable

## 22. QoS配置

# 在核心交换机上配置QoS
[LSW1]
# 流分类
traffic classifier voice operator and
 if-match dscp ef

traffic classifier video operator and
 if-match dscp af41

traffic classifier data operator and
 if-match dscp af21

# 流行为
traffic behavior voice
 car cir 1000 pir 2000 cbs 10000 pbs 20000 green pass red discard

traffic behavior video
 car cir 5000 pir 8000 cbs 50000 pbs 80000 green pass red discard

# 流策略
traffic policy campus_qos
 classifier voice behavior voice
 classifier video behavior video

# 应用到接口
interface GigabitEthernet0/0/1
 traffic-policy campus_qos inbound

## 23. 备份和恢复配置

# 配置自动备份
[LSW1]
archive
 path ftp://*************/backup/
 write-memory
 time-period 1440

# 配置文件保存
save force

## 配置完成说明
# 1. 所有设备已配置完成基础网络功能
# 2. 实现了多VLAN划分和隔离
# 3. 配置了端口安全防止MAC地址欺骗
# 4. 实现了无线网络覆盖
# 5. 配置了DHCP自动分配IP地址
# 6. 实现了防火墙安全策略
# 7. 配置了ACL访问控制
# 8. 实现了DNS和FTP服务
# 9. 配置了链路聚合提高可靠性
# 10. 实现了QoS服务质量保证

## 测试验证步骤
# 1. 验证VLAN间通信
# 2. 测试DHCP地址分配
# 3. 验证无线连接
# 4. 测试防火墙策略
# 5. 验证ACL访问控制
# 6. 测试DNS解析
# 7. 验证FTP服务
# 8. 测试链路聚合故障切换
